"""
Bot Manager - Advanced orchestrator for the Autonomous Bybit Trading Bot
Coordinates all trading activities, strategies, risk management, and emergency procedures
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from enum import Enum

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.exchange.bybit_client import BybitClient
from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
from bybit_bot.strategies.strategy_manager import StrategyManager
from bybit_bot.risk.risk_manager import RiskManager
from bybit_bot.analytics.performance_analyzer import PerformanceAnalyzer

logger = logging.getLogger("bybit_trading_bot.bot_manager")


class BotState(Enum):
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


class BotManager:
    """
    Advanced bot manager that orchestrates all trading bot components with:
    - State management and lifecycle control
    - Emergency stop procedures
    - Real-time risk monitoring
    - Performance tracking
    - Multi-strategy coordination
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager, hardware_monitor: HardwareMonitor):
        self.config = config
        self.db = database_manager
        self.hardware_monitor = hardware_monitor
        self.logger = TradingBotLogger(config)
        
        # Bot state management
        self.state = BotState.STOPPED
        self.running = False
        self.start_time = None
        self.last_cycle_time = None
        self.cycle_count = 0
        self.error_count = 0
        
        # Core components
        self.bybit_client = None
        self.strategy_manager = None
        self.risk_manager = None
        self.performance_analyzer = None
        
        # Trading state
        self.active_positions = {}
        self.pending_orders = {}
        self.total_pnl = 0.0
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        
        # Performance metrics
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'max_consecutive_losses': 0,
            'current_streak': 0
        }
        
        # Emergency stop triggers
        self.emergency_stop_triggers = {
            'max_daily_loss': getattr(self.config, 'max_daily_loss', 500.0),
            'max_drawdown': 15.0,  # 15% max drawdown
            'consecutive_losses': 10,
            'api_errors': 20,
            'risk_score_critical': 9.0
        }
        
        # Trading symbols
        self.trading_symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT"]
        
        # Async tasks
        self.main_task = None
        self.monitoring_tasks = []
        
    async def initialize(self):
        """Initialize all bot components with enhanced error handling"""
        try:
            self.logger.info("🚀 Initializing Advanced Bot Manager...")
            self.state = BotState.STARTING
            
            # Initialize Bybit client with connection testing
            self.bybit_client = BybitClient(self.config)
            await self.bybit_client.initialize()
            await self.bybit_client.test_connection()
            self.logger.info("✅ Bybit client initialized and tested")
            
            # Initialize strategy manager
            self.strategy_manager = StrategyManager(self.config, self.db, self.bybit_client)
            await self.strategy_manager.initialize()
            self.logger.info("✅ Strategy manager initialized")
            
            # Initialize risk manager with advanced features
            self.risk_manager = RiskManager(self.config, self.db, self.bybit_client)
            await self.risk_manager.initialize()
            self.logger.info("✅ Risk manager initialized")
            
            # Initialize performance analyzer
            self.performance_analyzer = PerformanceAnalyzer(self.config, self.db)
            await self.performance_analyzer.initialize()
            self.logger.info("✅ Performance analyzer initialized")
            
            # Load initial state and positions
            await self._load_initial_state()
            await self._load_trading_state()
            
            self.start_time = time.time()
            self.state = BotState.STOPPED  # Ready to start trading
            
            self.logger.info("🎯 Advanced Bot Manager initialized successfully!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Bot Manager: {e}")
            self.state = BotState.ERROR
            raise
    
    async def start(self):
        """Start the advanced trading bot with monitoring"""
        if self.running:
            self.logger.warning("Bot is already running")
            return
        
        try:
            self.logger.info("🚀 Starting Autonomous Bybit Trading Bot...")
            self.state = BotState.RUNNING
            self.running = True
            
            # Start main trading loop
            self.main_task = asyncio.create_task(self._main_trading_loop())
            
            # Start monitoring tasks
            self.monitoring_tasks = [
                asyncio.create_task(self._monitor_positions()),
                asyncio.create_task(self._monitor_performance()),
                asyncio.create_task(self._monitor_risk()),
                asyncio.create_task(self._emergency_monitoring()),
                asyncio.create_task(self._heartbeat_monitor()),
            ]
            
            self.logger.info("✅ Trading bot started with full monitoring")
            
            # Wait for tasks
            await asyncio.gather(self.main_task, *self.monitoring_tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            self.state = BotState.ERROR
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """Enhanced graceful shutdown with emergency procedures"""
        if not self.running:
            return
        
        self.logger.info("🛑 Shutting down Advanced Bot Manager...")
        self.state = BotState.STOPPING
        self.running = False
        
        try:
            # Cancel all pending orders
            await self._cancel_all_orders()
            
            # Close positions if configured
            if getattr(self.config, 'close_positions_on_shutdown', True):
                await self._close_all_positions()
            
            # Cancel all async tasks
            if self.main_task:
                self.main_task.cancel()
            
            for task in self.monitoring_tasks:
                task.cancel()
            
            # Save final state
            await self._save_trading_state()
            
            # Shutdown components
            if self.bybit_client:
                await self.bybit_client.close()
            
            if self.strategy_manager:
                await self.strategy_manager.shutdown()
            
            if self.risk_manager:
                await self.risk_manager.shutdown()
            
            if self.performance_analyzer:
                await self.performance_analyzer.shutdown()
            
            self.state = BotState.STOPPED
            self.logger.info("✅ Advanced Bot Manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
            self.state = BotState.ERROR
    
    async def pause(self):
        """Pause trading (keep monitoring but stop new trades)"""
        if self.state == BotState.RUNNING:
            self.state = BotState.PAUSED
            self.logger.info("⏸️ Trading bot paused")
    
    async def resume(self):
        """Resume trading from paused state"""
        if self.state == BotState.PAUSED:
            self.state = BotState.RUNNING
            self.logger.info("▶️ Trading bot resumed")
    
    async def emergency_stop(self, reason: str):
        """Emergency stop with immediate position closure"""
        self.logger.critical(f"🚨 EMERGENCY STOP: {reason}")
        
        try:
            # Immediately close all positions
            await self._emergency_close_positions()
            
            # Cancel all orders
            await self._cancel_all_orders()
            
            # Set error state
            self.state = BotState.ERROR
            self.running = False
            
            # Send emergency notification
            await self._send_emergency_notification(reason)
            
        except Exception as e:
            self.logger.error(f"Error during emergency stop: {e}")
    
    async def _main_trading_loop(self):
        """Enhanced main trading loop with error handling"""
        while self.running:
            try:
                cycle_start = time.time()
                self.cycle_count += 1
                
                self.logger.debug(f"🔄 Starting advanced trading cycle #{self.cycle_count}")
                
                # Skip trading if paused
                if self.state == BotState.PAUSED:
                    await asyncio.sleep(getattr(self.config, 'trading_cycle_interval', 30))
                    continue
                
                # Execute comprehensive trading cycle
                await self._execute_advanced_trading_cycle()
                
                # Update cycle timing
                self.last_cycle_time = time.time()
                cycle_duration = self.last_cycle_time - cycle_start
                
                self.logger.debug(f"✅ Cycle #{self.cycle_count} completed in {cycle_duration:.2f}s")
                
                # Reset error count on successful cycle
                self.error_count = 0
                
                # Wait for next cycle
                sleep_time = max(0, getattr(self.config, 'trading_cycle_interval', 30) - cycle_duration)
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                self.error_count += 1
                self.logger.error(f"❌ Error in trading cycle #{self.cycle_count}: {e}")
                
                # Check if too many errors
                if self.error_count >= self.emergency_stop_triggers['api_errors']:
                    await self.emergency_stop(f"Too many consecutive errors: {self.error_count}")
                    break
                
                # Exponential backoff on errors
                error_sleep = min(300, 10 * (2 ** min(self.error_count, 5)))
                await asyncio.sleep(error_sleep)
    
    async def _execute_advanced_trading_cycle(self):
        """Execute comprehensive trading cycle with all components"""
        # 1. Update market data and positions
        await self._update_market_state()
        
        # 2. Check global risk limits
        risk_check = await self.risk_manager.check_risk_limits() if self.risk_manager else {"allowed": True}
        if not risk_check.get("allowed", True):
            self.logger.warning(f"⚠️ Trading halted due to risk: {risk_check.get('reason', 'Unknown')}")
            return
        
        # 3. Process each trading symbol with advanced logic
        for symbol in self.trading_symbols:
            try:
                await self._process_symbol_advanced(symbol)
            except Exception as e:
                self.logger.error(f"Error processing {symbol}: {e}")
                continue
        
        # 4. Manage existing positions with advanced strategies
        await self._manage_positions_advanced()
        
        # 5. Update comprehensive performance metrics
        await self._update_performance_metrics_advanced()
        
        # 6. Log detailed cycle summary
        await self._log_cycle_summary_advanced()
    
    async def _process_symbol_advanced(self, symbol: str):
        """Advanced symbol processing with multiple strategies and risk checks"""
        try:
            # Get comprehensive market data
            market_data = await self.bybit_client.get_market_data(symbol, timeframe="1", limit=100)
            if not market_data:
                return
            
            current_price = market_data[-1]["close"]
            
            # Get signals from multiple strategies
            signals = await self.strategy_manager.get_trading_signals() if self.strategy_manager else []
            symbol_signals = [s for s in signals if s.get('symbol') == symbol]
            
            if not symbol_signals:
                return
            
            # Process the strongest signal
            best_signal = max(symbol_signals, key=lambda x: x.get('strength', 0))
            
            if best_signal['strength'] < getattr(self.config, 'min_signal_strength', 0.6):
                return
            
            # Check current position
            current_position = self.active_positions.get(symbol)
            
            # Process signal based on action
            if best_signal["action"] == "BUY":
                await self._process_buy_signal_advanced(symbol, best_signal, current_price, current_position)
            elif best_signal["action"] == "SELL":
                await self._process_sell_signal_advanced(symbol, best_signal, current_price, current_position)
            elif best_signal["action"] == "CLOSE":
                await self._process_close_signal_advanced(symbol, current_position)
            
        except Exception as e:
            self.logger.error(f"Error in advanced processing for {symbol}: {e}")
    
    async def _monitor_positions(self):
        """Enhanced position monitoring with risk alerts"""
        while self.running:
            try:
                for symbol in list(self.active_positions.keys()):
                    # Get current position P&L
                    position_pnl = await self._get_position_pnl(symbol)
                    
                    # Check for excessive losses
                    max_loss = getattr(self.config, 'max_loss_per_position', 100.0)
                    if position_pnl < -max_loss:
                        await self._close_position(symbol, "Max loss exceeded")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error monitoring positions: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_performance(self):
        """Enhanced performance monitoring with emergency triggers"""
        while self.running:
            try:
                # Update daily P&L
                await self._update_daily_pnl()
                
                # Check daily loss limit
                if self.daily_pnl < -self.emergency_stop_triggers['max_daily_loss']:
                    await self.emergency_stop(f"Daily loss limit exceeded: ${self.daily_pnl:.2f}")
                    break
                
                # Update performance metrics
                await self._calculate_performance_metrics()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error monitoring performance: {e}")
                await asyncio.sleep(600)
    
    async def _monitor_risk(self):
        """Enhanced risk monitoring with portfolio analysis"""
        while self.running:
            try:
                if not self.risk_manager:
                    await asyncio.sleep(180)
                    continue
                
                # Get portfolio risk score
                risk_score = await self.risk_manager.get_portfolio_risk_score()
                
                if risk_score >= self.emergency_stop_triggers['risk_score_critical']:
                    await self.emergency_stop(f"Critical risk level: {risk_score}")
                    break
                
                # Check correlation risk
                correlation_risk = await self.risk_manager.check_correlation_risk()
                if correlation_risk.get('high_correlation'):
                    self.logger.warning("⚠️ High correlation detected between positions")
                
                await asyncio.sleep(180)  # Check every 3 minutes
                
            except Exception as e:
                self.logger.error(f"Error monitoring risk: {e}")
                await asyncio.sleep(300)
    
    async def _emergency_monitoring(self):
        """Emergency monitoring for critical conditions"""
        while self.running:
            try:
                # Check consecutive losses
                consecutive_losses = await self._get_consecutive_losses()
                if consecutive_losses >= self.emergency_stop_triggers['consecutive_losses']:
                    await self.emergency_stop(f"Too many consecutive losses: {consecutive_losses}")
                    break
                
                # Check system health
                system_health = await self._check_system_health()
                if not system_health['healthy']:
                    await self.emergency_stop(f"System health critical: {system_health['reason']}")
                    break
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in emergency monitoring: {e}")
                await asyncio.sleep(120)
    
    async def _heartbeat_monitor(self):
        """Heartbeat monitoring to ensure bot is alive"""
        while self.running:
            try:
                # Update heartbeat in database
                await self._update_heartbeat()
                
                # Check for stale cycles
                if self.last_cycle_time:
                    time_since_cycle = time.time() - self.last_cycle_time
                    if time_since_cycle > 300:  # 5 minutes
                        self.logger.warning(f"⚠️ No trading cycle for {time_since_cycle:.0f} seconds")
                
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in heartbeat monitor: {e}")
                await asyncio.sleep(60)
    
    # Enhanced processing methods
    async def _process_buy_signal_advanced(self, symbol: str, signal: Dict, current_price: float, current_position: Optional[Dict]):
        """Advanced buy signal processing with risk management"""
        try:
            # If we have a short position, close it first
            if current_position and current_position.get("side") == "Sell":
                await self._close_position(symbol, "Closing short before long")
            
            # If we already have a long position, consider adding to it
            if current_position and current_position.get("side") == "Buy":
                # Check if we can add to position (implement pyramiding logic)
                if signal.get('strength', 0) > 0.8:  # High confidence
                    await self._add_to_position(symbol, signal, current_price)
                return
            
            # Calculate position size with advanced risk management
            if self.risk_manager:
                position_size = await self.risk_manager.calculate_position_size(
                    symbol, current_price, signal.get("confidence", 0.5)
                )
            else:
                # Fallback calculation
                account_balance = await self._get_account_balance()
                risk_per_trade = getattr(self.config, 'max_risk_percentage', 2.0) / 100
                position_size = (account_balance * risk_per_trade) / current_price
            
            if position_size <= 0:
                self.logger.debug(f"Position size too small for {symbol}, skipping")
                return
            
            # Calculate advanced stop loss and take profit
            stop_loss = await self._calculate_dynamic_stop_loss(symbol, "Buy", current_price, signal)
            take_profit = await self._calculate_dynamic_take_profit(symbol, "Buy", current_price, signal)
            
            # Place buy order
            order_result = await self.bybit_client.place_order(
                symbol=symbol,
                side="Buy",
                quantity=position_size,
                order_type="Market"
            )
            
            if order_result:
                # Record the trade
                trade_record = {
                    'order_id': order_result.get("order_id", str(int(time.time()))),
                    'symbol': symbol,
                    'side': 'Buy',
                    'quantity': position_size,
                    'entry_price': current_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'strategy': signal.get("strategy", "default"),
                    'timestamp': datetime.utcnow(),
                    'status': 'FILLED',
                    'signal_strength': signal.get('strength', 0.5)
                }
                
                await self._record_trade(trade_record)
                self.active_positions[symbol] = trade_record
                
                # Place stop loss and take profit orders
                if stop_loss:
                    await self._place_stop_loss_order(symbol, stop_loss, position_size)
                if take_profit:
                    await self._place_take_profit_order(symbol, take_profit, position_size)
                
                self.performance_metrics['total_trades'] += 1
                self.logger.info(f"✅ BUY order executed for {symbol}: {position_size:.4f} @ {current_price}")
            
        except Exception as e:
            self.logger.error(f"Error processing buy signal for {symbol}: {e}")
    
    async def _process_sell_signal_advanced(self, symbol: str, signal: Dict, current_price: float, current_position: Optional[Dict]):
        """Advanced sell signal processing with risk management"""
        try:
            # If we have a long position, close it first
            if current_position and current_position.get("side") == "Buy":
                await self._close_position(symbol, "Closing long before short")
            
            # If we already have a short position, consider adding to it
            if current_position and current_position.get("side") == "Sell":
                if signal.get('strength', 0) > 0.8:  # High confidence
                    await self._add_to_position(symbol, signal, current_price)
                return
            
            # Calculate position size
            if self.risk_manager:
                position_size = await self.risk_manager.calculate_position_size(
                    symbol, current_price, signal.get("confidence", 0.5)
                )
            else:
                account_balance = await self._get_account_balance()
                risk_per_trade = getattr(self.config, 'max_risk_percentage', 2.0) / 100
                position_size = (account_balance * risk_per_trade) / current_price
            
            if position_size <= 0:
                return
            
            # Calculate stop loss and take profit
            stop_loss = await self._calculate_dynamic_stop_loss(symbol, "Sell", current_price, signal)
            take_profit = await self._calculate_dynamic_take_profit(symbol, "Sell", current_price, signal)
            
            # Place sell order
            order_result = await self.bybit_client.place_order(
                symbol=symbol,
                side="Sell",
                quantity=position_size,
                order_type="Market"
            )
            
            if order_result:
                trade_record = {
                    'order_id': order_result.get("order_id", str(int(time.time()))),
                    'symbol': symbol,
                    'side': 'Sell',
                    'quantity': position_size,
                    'entry_price': current_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'strategy': signal.get("strategy", "default"),
                    'timestamp': datetime.utcnow(),
                    'status': 'FILLED',
                    'signal_strength': signal.get('strength', 0.5)
                }
                
                await self._record_trade(trade_record)
                self.active_positions[symbol] = trade_record
                
                # Place stop and limit orders
                if stop_loss:
                    await self._place_stop_loss_order(symbol, stop_loss, position_size)
                if take_profit:
                    await self._place_take_profit_order(symbol, take_profit, position_size)
                
                self.performance_metrics['total_trades'] += 1
                self.logger.info(f"✅ SELL order executed for {symbol}: {position_size:.4f} @ {current_price}")
            
        except Exception as e:
            self.logger.error(f"Error processing sell signal for {symbol}: {e}")
    
    async def _process_close_signal_advanced(self, symbol: str, current_position: Optional[Dict]):
        """Advanced close signal processing"""
        if current_position:
            await self._close_position(symbol, "Strategy close signal")
    
    # Position management methods
    async def _close_position(self, symbol: str, reason: str):
        """Close a specific position"""
        try:
            position = self.active_positions.get(symbol)
            if not position:
                return
            
            # Calculate exit side
            exit_side = "Sell" if position['side'] == "Buy" else "Buy"
            
            # Close position
            await self.bybit_client.place_order(
                symbol=symbol,
                side=exit_side,
                quantity=position['quantity'],
                order_type="Market",
                reduce_only=True
            )
            
            # Remove from active positions
            del self.active_positions[symbol]
            
            # Cancel related orders
            await self._cancel_related_orders(symbol)
            
            self.logger.info(f"🔒 Closed position for {symbol}: {reason}")
            
        except Exception as e:
            self.logger.error(f"Error closing position for {symbol}: {e}")
    
    async def _close_all_positions(self):
        """Close all active positions"""
        for symbol in list(self.active_positions.keys()):
            await self._close_position(symbol, "Bot shutdown")
    
    async def _emergency_close_positions(self):
        """Emergency close all positions immediately"""
        tasks = []
        for symbol in list(self.active_positions.keys()):
            tasks.append(self._close_position(symbol, "Emergency stop"))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _cancel_all_orders(self):
        """Cancel all pending orders"""
        try:
            for symbol in self.trading_symbols:
                if self.bybit_client:
                    await self.bybit_client.cancel_all_orders(symbol)
            
            self.pending_orders.clear()
            self.logger.info("🚫 All pending orders cancelled")
            
        except Exception as e:
            self.logger.error(f"Error cancelling orders: {e}")
    
    async def _cancel_related_orders(self, symbol: str):
        """Cancel orders related to a specific symbol"""
        try:
            if self.bybit_client:
                await self.bybit_client.cancel_all_orders(symbol)
            
            # Remove from pending orders tracking
            keys_to_remove = [key for key in self.pending_orders.keys() if symbol in key]
            for key in keys_to_remove:
                del self.pending_orders[key]
                
        except Exception as e:
            self.logger.error(f"Error cancelling orders for {symbol}: {e}")
    
    # Helper calculation methods
    async def _calculate_dynamic_stop_loss(self, symbol: str, side: str, entry_price: float, signal: Dict) -> Optional[float]:
        """Calculate dynamic stop loss based on volatility and signal strength"""
        try:
            # Base stop loss percentage
            base_stop_pct = getattr(self.config, 'stop_loss_percentage', 2.0) / 100
            
            # Adjust based on signal strength (stronger signals get tighter stops)
            strength_multiplier = 1.0 - (signal.get('strength', 0.5) * 0.3)
            stop_pct = base_stop_pct * strength_multiplier
            
            if side == "Buy":
                return entry_price * (1 - stop_pct)
            else:
                return entry_price * (1 + stop_pct)
                
        except Exception as e:
            self.logger.error(f"Error calculating stop loss: {e}")
            return None
    
    async def _calculate_dynamic_take_profit(self, symbol: str, side: str, entry_price: float, signal: Dict) -> Optional[float]:
        """Calculate dynamic take profit based on signal strength"""
        try:
            # Base take profit percentage
            base_tp_pct = getattr(self.config, 'take_profit_percentage', 4.0) / 100
            
            # Adjust based on signal strength (stronger signals get higher targets)
            strength_multiplier = 1.0 + (signal.get('strength', 0.5) * 0.5)
            tp_pct = base_tp_pct * strength_multiplier
            
            if side == "Buy":
                return entry_price * (1 + tp_pct)
            else:
                return entry_price * (1 - tp_pct)
                
        except Exception as e:
            self.logger.error(f"Error calculating take profit: {e}")
            return None
    
    # Data management methods
    async def _update_market_state(self):
        """Enhanced market state update"""
        try:
            # Update account balance
            self.account_balance = await self._get_account_balance()
            
            # Update positions from exchange
            exchange_positions = await self.bybit_client.get_positions() if self.bybit_client else []
            
            # Sync with our tracking
            for pos in exchange_positions:
                symbol = pos.get("symbol")
                if symbol and pos.get("size", 0) > 0:
                    if symbol not in self.active_positions:
                        # Found a position we weren't tracking
                        self.active_positions[symbol] = {
                            'symbol': symbol,
                            'side': pos.get("side"),
                            'quantity': pos.get("size"),
                            'entry_price': pos.get("avg_price", 0),
                            'timestamp': datetime.utcnow(),
                            'status': 'OPEN'
                        }
            
            # Remove closed positions
            for symbol in list(self.active_positions.keys()):
                if not any(pos.get("symbol") == symbol and pos.get("size", 0) > 0 for pos in exchange_positions):
                    del self.active_positions[symbol]
            
        except Exception as e:
            self.logger.error(f"Error updating market state: {e}")
    
    async def _get_account_balance(self) -> float:
        """Get current account balance"""
        try:
            if self.bybit_client:
                balance_info = await self.bybit_client.get_account_balance()
                return balance_info.get("totalWalletBalance", 1000.0)
            return 1000.0  # Fallback
        except Exception as e:
            self.logger.error(f"Error getting account balance: {e}")
            return 1000.0
    
    async def _get_position_pnl(self, symbol: str) -> float:
        """Get current P&L for a position"""
        try:
            if self.bybit_client:
                position = await self.bybit_client.get_position(symbol)
                return position.get('unrealisedPnl', 0.0) if position else 0.0
            return 0.0
        except Exception as e:
            self.logger.error(f"Error getting P&L for {symbol}: {e}")
            return 0.0
    
    # Placeholder methods for missing functionality
    async def _manage_positions_advanced(self):
        """Advanced position management"""
        # Implement advanced position management logic
        pass
    
    async def _update_performance_metrics_advanced(self):
        """Update advanced performance metrics"""
        # Update comprehensive performance metrics
        pass
    
    async def _log_cycle_summary_advanced(self):
        """Log advanced cycle summary"""
        if self.cycle_count % 100 == 0:  # Log every 100 cycles
            uptime = time.time() - self.start_time if self.start_time else 0
            self.logger.info(
                f"📊 Advanced Cycle #{self.cycle_count} | "
                f"Uptime: {uptime/3600:.1f}h | "
                f"Positions: {len(self.active_positions)} | "
                f"Daily P&L: ${self.daily_pnl:.2f} | "
                f"State: {self.state.value}"
            )
    
    async def _load_trading_state(self):
        """Load trading state from database"""
        # Load previous trading state if exists
        pass
    
    async def _save_trading_state(self):
        """Save current trading state"""
        # Save current state to database
        pass
    
    async def _record_trade(self, trade_record: Dict):
        """Record trade in database"""
        # Save trade record to database
        try:
            # Placeholder for database save operation
            self.logger.debug(f"Recorded trade: {trade_record}")
        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")
    
    async def _update_heartbeat(self):
        """Update bot heartbeat"""
        # Update heartbeat in database or monitoring system
        pass
    
    async def _update_daily_pnl(self):
        """Update daily P&L"""
        # Calculate and update daily P&L
        pass
    
    async def _get_consecutive_losses(self) -> int:
        """Get consecutive losses count"""
        # Count consecutive losing trades
        return 0
    
    async def _check_system_health(self) -> Dict:
        """Check system health"""
        return {'healthy': True, 'reason': None}
    
    async def _calculate_performance_metrics(self):
        """Calculate performance metrics"""
        # Update performance metrics
        pass
    
    async def _send_emergency_notification(self, reason: str):
        """Send emergency notification"""
        self.logger.critical(f"EMERGENCY NOTIFICATION: {reason}")
    
    async def _add_to_position(self, symbol: str, signal: Dict, current_price: float):
        """Add to existing position (pyramiding)"""
        # Implement position pyramiding logic
        self.logger.debug(f"Adding to position for {symbol} (not implemented)")
    
    async def _place_stop_loss_order(self, symbol: str, stop_price: float, quantity: float):
        """Place stop loss order"""
        # Place stop loss order
        pass
    
    async def _place_take_profit_order(self, symbol: str, profit_price: float, quantity: float):
        """Place take profit order"""
        # Place take profit order
        pass
    
    # Enhanced status method
    async def get_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status with all metrics"""
        try:
            uptime = time.time() - self.start_time if self.start_time else 0
            
            # Get account balance
            balance = await self._get_account_balance()
            
            # Get positions with P&L
            positions_with_pnl = {}
            for symbol, pos in self.active_positions.items():
                pnl = await self._get_position_pnl(symbol)
                positions_with_pnl[symbol] = {**pos, 'unrealized_pnl': pnl}
            
            status = {
                "bot_status": {
                    "state": self.state.value,
                    "running": self.running,
                    "uptime_seconds": uptime,
                    "uptime_formatted": f"{uptime/3600:.1f}h",
                    "cycle_count": self.cycle_count,
                    "error_count": self.error_count,
                    "last_cycle": self.last_cycle_time
                },
                "trading_metrics": {
                    **self.performance_metrics,
                    "total_pnl": self.total_pnl,
                    "daily_pnl": self.daily_pnl,
                    "max_drawdown": self.max_drawdown,
                    "active_positions_count": len(self.active_positions),
                    "pending_orders_count": len(self.pending_orders)
                },
                "account": {
                    "balance": balance,
                    "equity": balance + sum(p.get('unrealized_pnl', 0) for p in positions_with_pnl.values())
                },
                "positions": positions_with_pnl,
                "trading_symbols": self.trading_symbols,
                "risk_limits": self.emergency_stop_triggers
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            return {"error": str(e), "state": self.state.value}
    
    # Legacy compatibility methods
    async def execute_trading_cycle(self):
        """Legacy method for compatibility"""
        if self.state == BotState.RUNNING:
            await self._execute_advanced_trading_cycle()
    
    def get_positions(self) -> Dict:
        """Get current positions"""
        return self.active_positions.copy()
    
    def get_orders(self) -> Dict:
        """Get pending orders"""
        return self.pending_orders.copy()
        
    async def initialize(self):
        """Initialize all bot components"""
        try:
            self.logger.info("🚀 Initializing Bot Manager...")
            
            # Initialize Bybit client
            self.bybit_client = BybitClient(self.config)
            await self.bybit_client.initialize()
            self.logger.info("✅ Bybit client initialized")
            
            # Initialize strategy manager
            self.strategy_manager = StrategyManager(self.config, self.db, self.bybit_client)
            await self.strategy_manager.initialize()
            self.logger.info("✅ Strategy manager initialized")
            
            # Initialize risk manager
            self.risk_manager = RiskManager(self.config, self.db, self.bybit_client)
            await self.risk_manager.initialize()
            self.logger.info("✅ Risk manager initialized")
            
            # Initialize performance analyzer
            self.performance_analyzer = PerformanceAnalyzer(self.config, self.db)
            await self.performance_analyzer.initialize()
            self.logger.info("✅ Performance analyzer initialized")
            
            # Load initial state
            await self._load_initial_state()
            
            self.is_initialized = True
            self.start_time = datetime.utcnow()
            
            self.logger.info("🎯 Bot Manager initialized successfully!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Bot Manager: {e}")
            raise
    
    async def shutdown(self):
        """Gracefully shutdown all components"""
        try:
            self.logger.info("🛑 Shutting down Bot Manager...")
            
            self.is_trading = False
            
            # Close all positions if configured to do so
            if self.config.get("close_positions_on_shutdown", True):
                await self._close_all_positions()
            
            # Cancel all pending orders
            await self._cancel_all_orders()
            
            # Shutdown components
            if self.bybit_client:
                await self.bybit_client.close()
            
            if self.strategy_manager:
                await self.strategy_manager.shutdown()
            
            if self.risk_manager:
                await self.risk_manager.shutdown()
            
            if self.performance_analyzer:
                await self.performance_analyzer.shutdown()
            
            self.logger.info("✅ Bot Manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    async def execute_trading_cycle(self):
        """Execute one complete trading cycle"""
        try:
            if not self.is_initialized:
                raise RuntimeError("Bot manager not initialized")
            
            cycle_start_time = datetime.utcnow()
            self.cycle_count += 1
            
            self.logger.debug(f"🔄 Starting trading cycle #{self.cycle_count}")
            
            # 1. Update market data and positions
            await self._update_market_state()
            
            # 2. Check risk limits
            risk_check = await self.risk_manager.check_risk_limits()
            if not risk_check["allowed"]:
                self.logger.warning(f"⚠️ Trading halted due to risk limits: {risk_check['reason']}")
                return
            
            # 3. Process each trading symbol
            for symbol in self.trading_symbols:
                try:
                    await self._process_symbol(symbol)
                except Exception as e:
                    self.logger.error(f"Error processing {symbol}: {e}")
                    continue
            
            # 4. Update performance metrics
            await self._update_performance_metrics()
            
            # 5. Log cycle completion
            cycle_duration = (datetime.utcnow() - cycle_start_time).total_seconds()
            self.last_cycle_time = cycle_start_time
            
            self.logger.debug(f"✅ Trading cycle #{self.cycle_count} completed in {cycle_duration:.2f}s")
            
            # 6. Periodic tasks
            if self.cycle_count % 20 == 0:  # Every 20 cycles
                await self._run_periodic_tasks()
            
        except Exception as e:
            self.logger.error(f"❌ Error in trading cycle: {e}")
            raise
    
    async def _update_market_state(self):
        """Update market data and account state"""
        try:
            # Update account balance
            balance = await self.bybit_client.get_account_balance()
            
            # Update positions
            positions = await self.bybit_client.get_positions()
            self.active_positions = {pos["symbol"]: pos for pos in positions}
            
            # Log important changes
            if len(positions) > 0:
                total_unrealized = sum(pos["unrealized_pnl"] for pos in positions)
                self.logger.debug(f"💰 Open positions: {len(positions)}, Unrealized P&L: {total_unrealized:.2f}")
            
        except Exception as e:
            self.logger.error(f"Failed to update market state: {e}")
            raise
    
    async def _process_symbol(self, symbol: str):
        """Process trading signals for a specific symbol"""
        try:
            # Get current market data
            market_data = await self.bybit_client.get_market_data(symbol, timeframe="1", limit=100)
            if not market_data:
                return
            
            current_price = market_data[-1]["close"]
            
            # Get trading signal from strategy manager
            signal = await self.strategy_manager.get_trading_signal(symbol, market_data)
            
            if not signal or signal["action"] == "HOLD":
                return
            
            # Check if we have an existing position
            current_position = self.active_positions.get(symbol)
            
            # Process the signal
            if signal["action"] == "BUY":
                await self._process_buy_signal(symbol, signal, current_price, current_position)
            elif signal["action"] == "SELL":
                await self._process_sell_signal(symbol, signal, current_price, current_position)
            elif signal["action"] == "CLOSE":
                await self._process_close_signal(symbol, current_position)
            
        except Exception as e:
            self.logger.error(f"Error processing symbol {symbol}: {e}")
            raise
    
    async def _process_buy_signal(self, symbol: str, signal: Dict, current_price: float, current_position: Optional[Dict]):
        """Process a buy signal"""
        try:
            # If we have a short position, close it first
            if current_position and current_position["side"] == "Sell":
                await self.bybit_client.close_position(symbol)
                self.logger.info(f"🔄 Closed short position for {symbol} before opening long")
            
            # If we already have a long position, skip
            if current_position and current_position["side"] == "Buy":
                self.logger.debug(f"Already have long position for {symbol}, skipping")
                return
            
            # Calculate position size
            position_size = await self.risk_manager.calculate_position_size(
                symbol, current_price, signal.get("confidence", 0.5)
            )
            
            if position_size <= 0:
                self.logger.debug(f"Position size too small for {symbol}, skipping")
                return
            
            # Calculate stop loss and take profit
            stop_loss = current_price * (1 - self.config.stop_loss_percentage / 100)
            take_profit = current_price * (1 + self.config.take_profit_percentage / 100)
            
            # Place buy order
            order_result = await self.bybit_client.place_order(
                symbol=symbol,
                side="Buy",
                quantity=position_size,
                order_type="Market",
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            if order_result:
                # Save trade to database
                await self.db.save_trade({
                    "symbol": symbol,
                    "side": "buy",
                    "quantity": position_size,
                    "price": current_price,
                    "order_id": order_result["order_id"],
                    "strategy": signal.get("strategy", self.config.default_strategy),
                    "entry_price": current_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "metadata": {
                        "signal": signal,
                        "confidence": signal.get("confidence", 0.5)
                    }
                })
                
                self.total_trades += 1
                self.logger.log_trade(
                    action="BUY",
                    symbol=symbol,
                    quantity=position_size,
                    price=current_price,
                    order_id=order_result["order_id"]
                )
            
        except Exception as e:
            self.logger.error(f"Error processing buy signal for {symbol}: {e}")
    
    async def _process_sell_signal(self, symbol: str, signal: Dict, current_price: float, current_position: Optional[Dict]):
        """Process a sell signal"""
        try:
            # If we have a long position, close it first
            if current_position and current_position["side"] == "Buy":
                await self.bybit_client.close_position(symbol)
                self.logger.info(f"🔄 Closed long position for {symbol} before opening short")
            
            # If we already have a short position, skip
            if current_position and current_position["side"] == "Sell":
                self.logger.debug(f"Already have short position for {symbol}, skipping")
                return
            
            # Calculate position size
            position_size = await self.risk_manager.calculate_position_size(
                symbol, current_price, signal.get("confidence", 0.5)
            )
            
            if position_size <= 0:
                self.logger.debug(f"Position size too small for {symbol}, skipping")
                return
            
            # Calculate stop loss and take profit
            stop_loss = current_price * (1 + self.config.stop_loss_percentage / 100)
            take_profit = current_price * (1 - self.config.take_profit_percentage / 100)
            
            # Place sell order
            order_result = await self.bybit_client.place_order(
                symbol=symbol,
                side="Sell",
                quantity=position_size,
                order_type="Market",
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            if order_result:
                # Save trade to database
                await self.db.save_trade({
                    "symbol": symbol,
                    "side": "sell",
                    "quantity": position_size,
                    "price": current_price,
                    "order_id": order_result["order_id"],
                    "strategy": signal.get("strategy", self.config.default_strategy),
                    "entry_price": current_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "metadata": {
                        "signal": signal,
                        "confidence": signal.get("confidence", 0.5)
                    }
                })
                
                self.total_trades += 1
                self.logger.log_trade(
                    action="SELL",
                    symbol=symbol,
                    quantity=position_size,
                    price=current_price,
                    order_id=order_result["order_id"]
                )
            
        except Exception as e:
            self.logger.error(f"Error processing sell signal for {symbol}: {e}")
    
    async def _process_close_signal(self, symbol: str, current_position: Optional[Dict]):
        """Process a close position signal"""
        try:
            if not current_position:
                return
            
            success = await self.bybit_client.close_position(symbol)
            if success:
                self.logger.log_trade(
                    action="CLOSE",
                    symbol=symbol,
                    quantity=current_position["size"],
                    price=current_position["current_price"]
                )
            
        except Exception as e:
            self.logger.error(f"Error processing close signal for {symbol}: {e}")
    
    async def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            # Get recent performance data
            performance = await self.performance_analyzer.calculate_current_performance()
            
            if performance:
                self.total_pnl = performance.get("total_pnl", 0.0)
                self.winning_trades = performance.get("winning_trades", 0)
                self.current_drawdown = performance.get("current_drawdown", 0.0)
                self.max_drawdown = max(self.max_drawdown, self.current_drawdown)
                
                # Log performance periodically
                if self.cycle_count % 10 == 0:
                    self.logger.log_performance(
                        metric="Total_PNL",
                        value=self.total_pnl,
                        period="session"
                    )
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")
    
    async def _run_periodic_tasks(self):
        """Run periodic maintenance tasks"""
        try:
            self.logger.debug("🔧 Running periodic tasks...")
            
            # Clean up old data
            await self.db.cleanup_old_data(days_to_keep=30)
            
            # Optimize strategies if needed
            await self.strategy_manager.optimize_strategies()
            
            # Generate performance report
            await self.performance_analyzer.generate_performance_report()
            
            self.logger.debug("✅ Periodic tasks completed")
            
        except Exception as e:
            self.logger.error(f"Error in periodic tasks: {e}")
    
    async def _load_initial_state(self):
        """Load initial state from database"""
        try:
            # Load recent trades
            recent_trades = await self.db.get_trades(limit=100)
            self.total_trades = len(recent_trades)
            
            # Load performance metrics
            performance_data = await self.db.get_performance_data(days=7)
            if performance_data:
                latest = performance_data[0]
                self.total_pnl = latest.total_pnl
                self.winning_trades = latest.winning_trades
                self.max_drawdown = latest.max_drawdown
            
            self.logger.info(f"📊 Loaded initial state: {self.total_trades} trades, {self.total_pnl:.2f} PnL")
            
        except Exception as e:
            self.logger.error(f"Error loading initial state: {e}")
    
    async def _close_all_positions(self):
        """Close all open positions"""
        try:
            positions = await self.bybit_client.get_positions()
            
            for position in positions:
                await self.bybit_client.close_position(position["symbol"])
                self.logger.info(f"🔒 Closed position: {position['symbol']}")
            
        except Exception as e:
            self.logger.error(f"Error closing all positions: {e}")
    
    async def _cancel_all_orders(self):
        """Cancel all pending orders"""
        try:
            # This would need to be implemented based on how we track pending orders
            self.logger.info("🚫 All pending orders cancelled")
            
        except Exception as e:
            self.logger.error(f"Error cancelling orders: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status"""
        try:
            uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
            
            # Get account balance
            balance = await self.bybit_client.get_account_balance() if self.bybit_client else {}
            
            # Get positions
            positions = await self.bybit_client.get_positions() if self.bybit_client else []
            
            status = {
                "bot_status": {
                    "initialized": self.is_initialized,
                    "trading": self.is_trading,
                    "uptime_seconds": uptime,
                    "cycle_count": self.cycle_count,
                    "last_cycle": self.last_cycle_time.isoformat() if self.last_cycle_time else None
                },
                "trading_metrics": {
                    "total_trades": self.total_trades,
                    "winning_trades": self.winning_trades,
                    "win_rate": (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
                    "total_pnl": self.total_pnl,
                    "current_drawdown": self.current_drawdown,
                    "max_drawdown": self.max_drawdown
                },
                "account": balance,
                "positions": positions,
                "trading_symbols": self.trading_symbols,
                "config": {
                    "trading_enabled": self.config.trading_enabled,
                    "max_risk_percentage": self.config.max_risk_percentage,
                    "max_open_positions": self.config.max_open_positions,
                    "default_strategy": self.config.default_strategy
                }
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            return {"error": str(e)}
