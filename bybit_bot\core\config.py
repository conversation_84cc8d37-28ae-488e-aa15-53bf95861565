"""
Configuration management for the Autonomous Bybit Trading Bot
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class TradingConfig:
    """Trading configuration settings"""
    exchange: str
    environment: str
    max_risk_per_trade: float
    max_daily_loss: float
    max_position_size: float
    leverage_range: List[int]
    trading_pairs: List[str]
    strategies: Dict[str, Any]
    order_timeout: int
    slippage_tolerance: float
    trading_cycle_interval: int

@dataclass
class APIKeysConfig:
    """API keys configuration"""
    bybit: Dict[str, Any]
    newsapi: Dict[str, str]
    alpha_vantage: Dict[str, str]
    fred: Dict[str, str]
    twitter: Dict[str, str]
    reddit: Dict[str, str]

@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str
    pool_size: int
    max_overflow: int
    pool_timeout: int
    echo: bool

@dataclass
class HardwareConfig:
    """Hardware monitoring configuration"""
    check_interval: int
    cpu_temp_threshold: int
    memory_threshold: int
    disk_threshold: int
    auto_shutdown_on_critical: bool

@dataclass
class DataCrawlerConfig:
    """Data crawler configuration"""
    market_data_interval: int
    news_interval: int
    social_sentiment_interval: int
    economic_data_interval: int
    data_retention_days: int
    news_sources: List[str]
    social_keywords: List[str]
    economic_indicators: List[str]

@dataclass
class MLConfig:
    """Machine learning configuration"""
    retrain_interval: int
    prediction_horizon: int
    feature_lookback: int
    models: Dict[str, Any]
    features: Dict[str, bool]

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str
    format: str
    file: str
    max_file_size: str
    backup_count: int

@dataclass
class APIConfig:
    """API server configuration"""
    host: str
    port: int
    debug: bool
    workers: int

@dataclass
class SecurityConfig:
    """Security configuration"""
    jwt_secret: str
    jwt_expiry_hours: int
    rate_limit_per_minute: int

@dataclass
class NotificationsConfig:
    """Notifications configuration"""
    enabled: bool
    channels: Dict[str, Any]
    triggers: Dict[str, Any]

@dataclass
class BacktestingConfig:
    """Backtesting configuration"""
    enabled: bool
    start_date: str
    end_date: str
    initial_balance: float
    commission: float

@dataclass
class DevelopmentConfig:
    """Development configuration"""
    paper_trading: bool
    debug_mode: bool
    log_trades: bool
    save_predictions: bool


class BotConfig:
    """Main configuration class for the trading bot"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.yaml"
        self.config_path = Path(self.config_file)
        
        # Load configuration
        self._load_config()
        
    def _load_config(self):
        """Load configuration from YAML file or environment variables"""
        if self.config_path.exists():
            with open(self.config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            # Use template config if no config file exists
            template_path = Path("config_template.yaml")
            if template_path.exists():
                with open(template_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                # Save as config.yaml
                with open(self.config_path, 'w') as f:
                    yaml.dump(config_data, f, default_flow_style=False)
            else:
                config_data = self._get_default_config()
        
        # Override with environment variables where available
        self._override_with_env_vars(config_data)
        
        # Parse into configuration objects
        self._parse_config(config_data)
    
    def _override_with_env_vars(self, config_data: Dict[str, Any]):
        """Override configuration with environment variables"""
        # Bybit API keys
        if os.getenv("BYBIT_API_KEY"):
            config_data["api_keys"]["bybit"]["api_key"] = os.getenv("BYBIT_API_KEY")
        if os.getenv("BYBIT_API_SECRET"):
            config_data["api_keys"]["bybit"]["api_secret"] = os.getenv("BYBIT_API_SECRET")
        
        # Database URL
        if os.getenv("DATABASE_URL"):
            config_data["database"]["url"] = os.getenv("DATABASE_URL")
        
        # News API
        if os.getenv("NEWSAPI_KEY"):
            config_data["api_keys"]["newsapi"]["api_key"] = os.getenv("NEWSAPI_KEY")
        
        # Alpha Vantage
        if os.getenv("ALPHA_VANTAGE_KEY"):
            config_data["api_keys"]["alpha_vantage"]["api_key"] = os.getenv("ALPHA_VANTAGE_KEY")
        
        # FRED API
        if os.getenv("FRED_API_KEY"):
            config_data["api_keys"]["fred"]["api_key"] = os.getenv("FRED_API_KEY")
        
        # Twitter API
        if os.getenv("TWITTER_BEARER_TOKEN"):
            config_data["api_keys"]["twitter"]["bearer_token"] = os.getenv("TWITTER_BEARER_TOKEN")
        
        # Reddit API
        if os.getenv("REDDIT_CLIENT_ID"):
            config_data["api_keys"]["reddit"]["client_id"] = os.getenv("REDDIT_CLIENT_ID")
        if os.getenv("REDDIT_CLIENT_SECRET"):
            config_data["api_keys"]["reddit"]["client_secret"] = os.getenv("REDDIT_CLIENT_SECRET")
    
    def _parse_config(self, config_data: Dict[str, Any]):
        """Parse configuration data into structured objects"""
        self.trading = TradingConfig(**config_data["trading"])
        self.api_keys = APIKeysConfig(**config_data["api_keys"])
        self.database = DatabaseConfig(**config_data["database"])
        self.hardware = HardwareConfig(**config_data["hardware"])
        self.data_crawler = DataCrawlerConfig(**config_data["data_crawler"])
        self.ml = MLConfig(**config_data["ml"])
        self.logging = LoggingConfig(**config_data["logging"])
        self.api = APIConfig(**config_data["api"])
        self.security = SecurityConfig(**config_data["security"])
        self.notifications = NotificationsConfig(**config_data["notifications"])
        self.backtesting = BacktestingConfig(**config_data["backtesting"])
        self.development = DevelopmentConfig(**config_data["development"])
        
        # Convenience properties
        self.api_host = self.api.host
        self.api_port = self.api.port
        self.debug_mode = self.development.debug_mode
        self.paper_trading = self.development.paper_trading
        self.trading_cycle_interval = self.trading.trading_cycle_interval
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "trading": {
                "exchange": "bybit",
                "environment": "testnet",
                "max_risk_per_trade": 0.02,
                "max_daily_loss": 0.10,
                "max_position_size": 0.25,
                "leverage_range": [1, 10],
                "trading_pairs": ["BTCUSDT", "ETHUSDT"],
                "strategies": {
                    "momentum": {"enabled": True, "weight": 0.5},
                    "mean_reversion": {"enabled": True, "weight": 0.5}
                },
                "order_timeout": 30,
                "slippage_tolerance": 0.005,
                "trading_cycle_interval": 30
            },
            "api_keys": {
                "bybit": {"api_key": "", "api_secret": "", "testnet": True},
                "newsapi": {"api_key": ""},
                "alpha_vantage": {"api_key": ""},
                "fred": {"api_key": ""},
                "twitter": {"bearer_token": ""},
                "reddit": {"client_id": "", "client_secret": "", "user_agent": "BybitBot/1.0"}
            },
            "database": {
                "url": "postgresql://username:password@localhost:5432/bybit_bot",
                "pool_size": 20,
                "max_overflow": 50,
                "pool_timeout": 30,
                "echo": False
            },
            "hardware": {
                "check_interval": 60,
                "cpu_temp_threshold": 80,
                "memory_threshold": 85,
                "disk_threshold": 90,
                "auto_shutdown_on_critical": True
            },
            "data_crawler": {
                "market_data_interval": 30,
                "news_interval": 300,
                "social_sentiment_interval": 600,
                "economic_data_interval": 3600,
                "data_retention_days": 90,
                "news_sources": ["coindesk", "cointelegraph"],
                "social_keywords": ["bitcoin", "ethereum", "crypto"],
                "economic_indicators": ["GDP", "INFLATION", "DXY"]
            },
            "ml": {
                "retrain_interval": 86400,
                "prediction_horizon": 300,
                "feature_lookback": 100,
                "models": {"xgboost": {"n_estimators": 100}},
                "features": {"technical_indicators": True}
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/trading_bot.log",
                "max_file_size": "10MB",
                "backup_count": 5
            },
            "api": {
                "host": "0.0.0.0",
                "port": 8000,
                "debug": False,
                "workers": 1
            },
            "security": {
                "jwt_secret": "change-this-secret-key",
                "jwt_expiry_hours": 24,
                "rate_limit_per_minute": 60
            },
            "notifications": {
                "enabled": False,
                "channels": {},
                "triggers": {}
            },
            "backtesting": {
                "enabled": True,
                "start_date": "2023-01-01",
                "end_date": "2024-01-01",
                "initial_balance": 10000,
                "commission": 0.001
            },
            "development": {
                "paper_trading": True,
                "debug_mode": False,
                "log_trades": True,
                "save_predictions": True
            }
        }
    
    def get_trading_pairs(self) -> List[str]:
        """Get list of trading pairs"""
        return self.trading.trading_pairs
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """Get configuration for a specific strategy"""
        return self.trading.strategies.get(strategy_name, {})
    
    def is_strategy_enabled(self, strategy_name: str) -> bool:
        """Check if a strategy is enabled"""
        strategy_config = self.get_strategy_config(strategy_name)
        return strategy_config.get("enabled", False)
    
    def get_api_key(self, service: str) -> Dict[str, Any]:
        """Get API key configuration for a service"""
        return getattr(self.api_keys, service, {})
    
    def save_config(self):
        """Save current configuration to file"""
        config_data = {
            "trading": self.trading.__dict__,
            "api_keys": self.api_keys.__dict__,
            "database": self.database.__dict__,
            "hardware": self.hardware.__dict__,
            "data_crawler": self.data_crawler.__dict__,
            "ml": self.ml.__dict__,
            "logging": self.logging.__dict__,
            "api": self.api.__dict__,
            "security": self.security.__dict__,
            "notifications": self.notifications.__dict__,
            "backtesting": self.backtesting.__dict__,
            "development": self.development.__dict__
        }
        
        with open(self.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Check required API keys
        if not self.api_keys.bybit.get("api_key") or not self.api_keys.bybit.get("api_secret"):
            errors.append("Bybit API credentials are required")
        
        # Check trading pairs
        if not self.trading.trading_pairs:
            errors.append("At least one trading pair must be configured")
        
        # Check strategies
        enabled_strategies = [name for name, config in self.trading.strategies.items() 
                            if config.get("enabled", False)]
        if not enabled_strategies:
            errors.append("At least one trading strategy must be enabled")
        
        # Check database URL
        if not self.database.url or "username:password" in self.database.url:
            errors.append("Database connection URL must be properly configured")
        
        return errors
