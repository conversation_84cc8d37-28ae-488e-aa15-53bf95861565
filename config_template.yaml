# Autonomous Bybit Trading Bot Configuration
# Copy this file to config.yaml and update with your credentials

# ================================
# TRADING CONFIGURATION
# ================================
trading:
  # Exchange Settings
  exchange: "bybit"
  environment: "testnet"  # testnet or mainnet
  
  # Account Settings
  max_risk_per_trade: 0.02  # 2% max risk per trade
  max_daily_loss: 0.10      # 10% max daily loss
  max_position_size: 0.25   # 25% max position size
  leverage_range: [1, 10]   # Min/Max leverage
  
  # Trading Pairs
  trading_pairs:
    - "BTCUSDT"
    - "ETHUSDT"
    - "ADAUSDT"
    - "SOLUSDT"
    - "DOTUSDT"
  
  # Strategy Configuration
  strategies:
    momentum:
      enabled: true
      weight: 0.3
      lookback_period: 14
      rsi_threshold: [30, 70]
    
    mean_reversion:
      enabled: true
      weight: 0.3
      bollinger_period: 20
      bollinger_std: 2.0
    
    trend_following:
      enabled: true
      weight: 0.4
      ema_short: 12
      ema_long: 26
      signal_period: 9
  
  # Execution Settings
  order_timeout: 30
  slippage_tolerance: 0.005
  trading_cycle_interval: 30  # seconds

# ================================
# API CREDENTIALS
# ================================
api_keys:
  # Bybit API Keys
  bybit:
    api_key: "YOUR_BYBIT_API_KEY"
    api_secret: "YOUR_BYBIT_API_SECRET"
    testnet: true
  
  # News & Sentiment APIs
  newsapi:
    api_key: "YOUR_NEWSAPI_KEY"
  
  alpha_vantage:
    api_key: "YOUR_ALPHA_VANTAGE_KEY"
  
  fred:
    api_key: "YOUR_FRED_API_KEY"
  
  # Social Media APIs
  twitter:
    bearer_token: "YOUR_TWITTER_BEARER_TOKEN"
    api_key: "YOUR_TWITTER_API_KEY"
    api_secret: "YOUR_TWITTER_API_SECRET"
    access_token: "YOUR_TWITTER_ACCESS_TOKEN"
    access_token_secret: "YOUR_TWITTER_ACCESS_TOKEN_SECRET"
  
  reddit:
    client_id: "YOUR_REDDIT_CLIENT_ID"
    client_secret: "YOUR_REDDIT_CLIENT_SECRET"
    user_agent: "BybitBot/1.0"

# ================================
# DATABASE CONFIGURATION
# ================================
database:
  url: "postgresql://username:password@localhost:5432/bybit_bot"
  pool_size: 20
  max_overflow: 50
  pool_timeout: 30
  echo: false

# ================================
# HARDWARE MONITORING
# ================================
hardware:
  check_interval: 60  # seconds
  cpu_temp_threshold: 80  # Celsius
  memory_threshold: 85    # Percentage
  disk_threshold: 90      # Percentage
  auto_shutdown_on_critical: true

# ================================
# DATA CRAWLER CONFIGURATION
# ================================
data_crawler:
  # Update intervals in seconds
  market_data_interval: 30
  news_interval: 300      # 5 minutes
  social_sentiment_interval: 600  # 10 minutes
  economic_data_interval: 3600    # 1 hour
  
  # Data retention (days)
  data_retention_days: 90
  
  # News sources
  news_sources:
    - "coindesk"
    - "cointelegraph"
    - "crypto-news"
    - "bitcoin-magazine"
  
  # Social media keywords
  social_keywords:
    - "bitcoin"
    - "ethereum"
    - "crypto"
    - "btc"
    - "eth"
    - "defi"
    - "nft"
  
  # Economic indicators
  economic_indicators:
    - "GDP"
    - "INFLATION"
    - "UNEMPLOYMENT"
    - "INTEREST_RATES"
    - "DXY"  # Dollar Index

# ================================
# MACHINE LEARNING CONFIGURATION
# ================================
ml:
  # Model settings
  retrain_interval: 86400  # 24 hours in seconds
  prediction_horizon: 300  # 5 minutes
  feature_lookback: 100    # Number of historical points
  
  # Model parameters
  models:
    xgboost:
      n_estimators: 100
      max_depth: 6
      learning_rate: 0.1
    
    lstm:
      sequence_length: 60
      hidden_units: 50
      dropout: 0.2
      epochs: 50
  
  # Feature engineering
  features:
    technical_indicators: true
    sentiment_scores: true
    economic_data: true
    volume_profile: true
    order_book_imbalance: true

# ================================
# LOGGING CONFIGURATION
# ================================
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/trading_bot.log"
  max_file_size: "10MB"
  backup_count: 5

# ================================
# API SERVER CONFIGURATION
# ================================
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  workers: 1

# ================================
# SECURITY SETTINGS
# ================================
security:
  jwt_secret: "your-secret-key-change-this"
  jwt_expiry_hours: 24
  rate_limit_per_minute: 60
  
# ================================
# NOTIFICATION SETTINGS
# ================================
notifications:
  enabled: true
  channels:
    email:
      enabled: false
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "your-app-password"
    
    webhook:
      enabled: false
      url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
  
  # Notification triggers
  triggers:
    large_profit: 0.05      # 5% profit
    large_loss: -0.03       # 3% loss
    system_error: true
    hardware_warning: true
    daily_summary: true

# ================================
# BACKTESTING CONFIGURATION
# ================================
backtesting:
  enabled: true
  start_date: "2023-01-01"
  end_date: "2024-01-01"
  initial_balance: 10000
  commission: 0.001  # 0.1%
  
# ================================
# DEVELOPMENT SETTINGS
# ================================
development:
  paper_trading: true
  debug_mode: false
  log_trades: true
  save_predictions: true
