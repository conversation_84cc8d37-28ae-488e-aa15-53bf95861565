#!/usr/bin/env python3
"""
Autonomous Bybit Trading Bot with Advanced Data Crawler
Main Entry Point

Continuously trades on Bybit for profit, adapts strategies using persistent learning,
and monitors system health for safe operation.
Features advanced data crawler for market intelligence.
"""

import asyncio
import logging
import signal
import sys
import threading
import time
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, List
import os
from pathlib import Path

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import pandas as pd
import numpy as np

# Import our trading bot components
from bybit_bot.core.bot_manager import BotManager
from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import setup_logging
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
from bybit_bot.ml.market_predictor import MarketPredictor
from bybit_bot.strategies.adaptive_strategy_engine import AdaptiveStrategyEngine

# Global variables
bot_manager: Optional[BotManager] = None
hardware_monitor: Optional[HardwareMonitor] = None
database_manager: Optional[DatabaseManager] = None
market_data_crawler: Optional[MarketDataCrawler] = None
news_sentiment_crawler: Optional[NewsSentimentCrawler] = None
social_sentiment_crawler: Optional[SocialSentimentCrawler] = None
economic_data_crawler: Optional[EconomicDataCrawler] = None
market_predictor: Optional[MarketPredictor] = None
adaptive_strategy_engine: Optional[AdaptiveStrategyEngine] = None
is_shutting_down = False

# FastAPI app
app = FastAPI(
    title="Autonomous Bybit Trading Bot with Advanced Data Crawler",
    description="Continuously trades on Bybit for profit with adaptive learning and advanced market intelligence",
    version="2.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Routes
@app.get("/")
async def root():
    """Root endpoint with bot status"""
    return {
        "message": "Autonomous Bybit Trading Bot with Advanced Data Crawler",
        "status": "running" if bot_manager and bot_manager.running else "stopped",
        "timestamp": datetime.utcnow().isoformat(),
        "features": [
            "Real-time market data collection",
            "News sentiment analysis",
            "Social media sentiment tracking",
            "Economic indicators monitoring",
            "Machine learning predictions",
            "Adaptive strategy optimization",
            "Hardware health monitoring",
            "Cross-exchange arbitrage detection"
        ]
    }

@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    health_status = {
        "overall": "healthy",
        "components": {},
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Check hardware monitor
    if hardware_monitor:
        hw_status = await hardware_monitor.get_system_status()
        health_status["components"]["hardware"] = hw_status
        if hw_status.get("overall_status") != "healthy":
            health_status["overall"] = "degraded"
    
    # Check database
    if database_manager:
        try:
            await database_manager.execute("SELECT 1")
            health_status["components"]["database"] = "healthy"
        except Exception:
            health_status["components"]["database"] = "unhealthy"
            health_status["overall"] = "unhealthy"
    
    # Check trading bot
    if bot_manager:
        health_status["components"]["trading_bot"] = {
            "status": "running" if bot_manager.running else "stopped",
            "uptime": time.time() - bot_manager.start_time if hasattr(bot_manager, 'start_time') else 0
        }
    
    # Check data crawlers
    crawlers = {
        "market_data": market_data_crawler,
        "news_sentiment": news_sentiment_crawler,
        "social_sentiment": social_sentiment_crawler,
        "economic_data": economic_data_crawler
    }
    
    for name, crawler in crawlers.items():
        health_status["components"][f"{name}_crawler"] = "running" if crawler and crawler.running else "stopped"
    
    # Check ML predictor
    if market_predictor:
        health_status["components"]["ml_predictor"] = "active" if market_predictor.model_loaded else "inactive"
    
    return health_status

@app.get("/status")
async def get_comprehensive_status():
    """Get comprehensive system status"""
    status = {
        "timestamp": datetime.utcnow().isoformat(),
        "system": {},
        "trading": {},
        "data_crawlers": {},
        "ml_models": {},
        "market_data": {}
    }
    
    # Trading status
    if bot_manager:
        status["trading"] = await bot_manager.get_status()
    
    # Hardware status
    if hardware_monitor:
        status["system"] = await hardware_monitor.get_system_status()
    
    # Data crawler status
    if market_data_crawler:
        status["data_crawlers"]["market_data"] = {
            "running": market_data_crawler.running,
            "last_update": "real-time"
        }
    
    if news_sentiment_crawler:
        # Get recent sentiment summary
        sentiment_summary = await news_sentiment_crawler.get_sentiment_summary(24)
        status["data_crawlers"]["news_sentiment"] = {
            "running": news_sentiment_crawler.running,
            "sentiment_summary": sentiment_summary
        }
    
    # ML model status
    if market_predictor:
        status["ml_models"] = await market_predictor.get_model_status()
    
    # Recent market data
    if market_data_crawler:
        for symbol in ["BTCUSDT", "ETHUSDT"]:
            latest_data = await market_data_crawler.get_latest_market_data(symbol)
            if latest_data:
                status["market_data"][symbol] = {
                    "price": latest_data.get("price"),
                    "change_24h": latest_data.get("change_percent_24h"),
                    "volume": latest_data.get("volume"),
                    "last_update": latest_data.get("timestamp")
                }
    
    return status

@app.get("/market-data/{symbol}")
async def get_market_data(symbol: str):
    """Get latest market data for a symbol"""
    if not market_data_crawler:
        raise HTTPException(status_code=503, detail="Market data crawler not available")
    
    latest_data = await market_data_crawler.get_latest_market_data(symbol.upper())
    if not latest_data:
        raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
    
    # Get additional indicators
    indicators = await market_data_crawler.get_market_indicators(symbol.upper(), limit=1)
    
    return {
        "symbol": symbol.upper(),
        "market_data": latest_data,
        "indicators": indicators[0] if indicators else None,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/sentiment")
async def get_sentiment_analysis():
    """Get current sentiment analysis"""
    if not news_sentiment_crawler:
        raise HTTPException(status_code=503, detail="News sentiment crawler not available")
    
    # Get sentiment summary
    sentiment_summary = await news_sentiment_crawler.get_sentiment_summary(24)
    
    # Get trending topics
    trending_topics = await news_sentiment_crawler.get_trending_topics(10)
    
    # Get recent high-impact news
    significant_news = await news_sentiment_crawler.get_news_by_sentiment(0.5, 10)
    
    return {
        "sentiment_summary": sentiment_summary,
        "trending_topics": trending_topics,
        "significant_news": significant_news,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/arbitrage")
async def get_arbitrage_opportunities():
    """Get current arbitrage opportunities"""
    if not market_data_crawler:
        raise HTTPException(status_code=503, detail="Market data crawler not available")
    
    opportunities = await market_data_crawler.get_arbitrage_opportunities(min_profit=0.5)
    
    return {
        "opportunities": opportunities,
        "count": len(opportunities),
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/predictions/{symbol}")
async def get_price_predictions(symbol: str):
    """Get ML price predictions for a symbol"""
    if not market_predictor:
        raise HTTPException(status_code=503, detail="ML predictor not available")
    
    predictions = await market_predictor.get_predictions(symbol.upper())
    
    return {
        "symbol": symbol.upper(),
        "predictions": predictions,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.post("/control/start")
async def start_trading():
    """Start trading endpoint"""
    try:
        if bot_manager:
            await bot_manager.start_trading()
            return {"message": "Trading started successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="Bot manager not initialized")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start trading: {e}")

@app.post("/control/stop")
async def stop_trading():
    """Stop trading endpoint"""
    try:
        if bot_manager:
            await bot_manager.stop_trading()
            return {"message": "Trading stopped successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="Bot manager not initialized")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop trading: {e}")

@app.post("/control/retrain-models")
async def retrain_ml_models():
    """Retrain ML models endpoint"""
    try:
        if market_predictor:
            await market_predictor.retrain_models()
            return {"message": "ML models retrained successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="ML predictor not available")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrain models: {e}")

@app.get("/performance")
async def get_performance_metrics():
    """Get trading performance metrics"""
    if not bot_manager:
        raise HTTPException(status_code=503, detail="Bot manager not available")
    
    performance = await bot_manager.get_performance_metrics()
    
    return {
        "performance": performance,
        "timestamp": datetime.utcnow().isoformat()
    }


class TradingBotService:
    """Advanced trading bot service with data crawlers and ML"""
    
    def __init__(self):
        self.config = BotConfig()
        self.logger = setup_logging()
        self.running = False
        self.main_loop_task = None
        self.start_time = None
        
    async def initialize(self):
        """Initialize all bot components"""
        global bot_manager, hardware_monitor, database_manager, market_data_crawler, \
                news_sentiment_crawler, social_sentiment_crawler, economic_data_crawler, \
                market_predictor, adaptive_strategy_engine
        
        try:
            self.logger.info("🚀 Initializing Autonomous Bybit Trading Bot with Advanced Data Crawler...")
            
            # Validate configuration
            config_errors = self.config.validate_config()
            if config_errors:
                self.logger.error(f"Configuration errors: {config_errors}")
                raise ValueError(f"Configuration validation failed: {config_errors}")
            
            # Initialize database
            database_manager = DatabaseManager(self.config)
            await database_manager.initialize()
            self.logger.info("✅ Database initialized")
            
            # Initialize hardware monitor
            hardware_monitor = HardwareMonitor(self.config)
            await hardware_monitor.start()
            self.logger.info("✅ Hardware monitor started")
            
            # Initialize data crawlers
            market_data_crawler = MarketDataCrawler(self.config, database_manager)
            news_sentiment_crawler = NewsSentimentCrawler(self.config, database_manager)
            social_sentiment_crawler = SocialSentimentCrawler(self.config, database_manager)
            economic_data_crawler = EconomicDataCrawler(self.config, database_manager)
            
            # Start data crawlers
            await market_data_crawler.start()
            self.logger.info("✅ Market data crawler started")
            
            await news_sentiment_crawler.start()
            self.logger.info("✅ News sentiment crawler started")
            
            await social_sentiment_crawler.start()
            self.logger.info("✅ Social sentiment crawler started")
            
            await economic_data_crawler.start()
            self.logger.info("✅ Economic data crawler started")
            
            # Initialize ML predictor
            market_predictor = MarketPredictor(self.config, database_manager)
            await market_predictor.initialize()
            self.logger.info("✅ ML predictor initialized")
            
            # Initialize adaptive strategy engine
            adaptive_strategy_engine = AdaptiveStrategyEngine(
                self.config, database_manager, market_predictor
            )
            await adaptive_strategy_engine.initialize()
            self.logger.info("✅ Adaptive strategy engine initialized")
            
            # Initialize bot manager
            bot_manager = BotManager(
                self.config, database_manager, hardware_monitor,
                market_data_crawler, news_sentiment_crawler,
                social_sentiment_crawler, economic_data_crawler,
                market_predictor, adaptive_strategy_engine
            )
            await bot_manager.initialize()
            self.logger.info("✅ Bot manager initialized")
            
            self.logger.info("🎯 Advanced trading bot initialization complete - Ready to trade with AI!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize bot: {e}")
            raise
    
    async def start_trading(self):
        """Start the main trading loop"""
        if self.running:
            self.logger.warning("Trading bot is already running")
            return
            
        self.running = True
        self.start_time = time.time()
        self.logger.info("🔄 Starting autonomous trading with advanced data intelligence...")
        
        # Start the main trading loop
        self.main_loop_task = asyncio.create_task(self._main_trading_loop())
        
    async def stop_trading(self):
        """Stop the trading bot gracefully"""
        global is_shutting_down
        is_shutting_down = True
        
        if not self.running:
            return
            
        self.logger.info("🛑 Stopping advanced trading bot...")
        self.running = False
        
        if self.main_loop_task:
            self.main_loop_task.cancel()
            try:
                await self.main_loop_task
            except asyncio.CancelledError:
                pass
        
        # Stop all components
        components = [
            ("Bot Manager", bot_manager),
            ("Market Data Crawler", market_data_crawler),
            ("News Sentiment Crawler", news_sentiment_crawler),
            ("Social Sentiment Crawler", social_sentiment_crawler),
            ("Economic Data Crawler", economic_data_crawler),
            ("Hardware Monitor", hardware_monitor),
            ("Database Manager", database_manager)
        ]
        
        for name, component in components:
            if component:
                try:
                    if hasattr(component, 'shutdown'):
                        await component.shutdown()
                    elif hasattr(component, 'stop'):
                        await component.stop()
                    elif hasattr(component, 'close'):
                        await component.close()
                    self.logger.info(f"✅ {name} stopped")
                except Exception as e:
                    self.logger.error(f"❌ Error stopping {name}: {e}")
            
        self.logger.info("✅ Advanced trading bot stopped gracefully")
    
    async def _main_trading_loop(self):
        """Main continuous trading loop with advanced intelligence"""
        self.logger.info("🔄 Starting main trading loop with AI-powered decision making...")
        
        try:
            cycle_count = 0
            
            while self.running and not is_shutting_down:
                try:
                    cycle_start_time = time.time()
                    cycle_count += 1
                    
                    # Check hardware health first
                    if not await hardware_monitor.check_system_health():
                        self.logger.warning("⚠️ Hardware issues detected - pausing trading")
                        await asyncio.sleep(60)
                        continue
                    
                    # Execute advanced trading cycle
                    await self._execute_advanced_trading_cycle(cycle_count)
                    
                    # Calculate cycle time
                    cycle_time = time.time() - cycle_start_time
                    self.logger.info(f"🔄 Trading cycle {cycle_count} completed in {cycle_time:.2f}s")
                    
                    # Wait before next cycle
                    sleep_time = max(0, self.config.trading_cycle_interval - cycle_time)
                    if sleep_time > 0:
                        await asyncio.sleep(sleep_time)
                    
                except Exception as e:
                    self.logger.error(f"❌ Error in trading cycle {cycle_count}: {e}")
                    await asyncio.sleep(30)  # Wait before retrying
                    
        except asyncio.CancelledError:
            self.logger.info("🛑 Trading loop cancelled")
        except Exception as e:
            self.logger.error(f"💥 Fatal error in trading loop: {e}")
            self.running = False
    
    async def _execute_advanced_trading_cycle(self, cycle_count: int):
        """Execute one advanced trading cycle with all intelligence systems"""
        
        # Step 1: Collect latest market intelligence
        market_intelligence = await self._gather_market_intelligence()
        
        # Step 2: Update ML predictions
        if cycle_count % 5 == 0:  # Every 5 cycles
            await market_predictor.update_predictions()
        
        # Step 3: Adapt strategies based on new data
        if cycle_count % 10 == 0:  # Every 10 cycles
            await adaptive_strategy_engine.adapt_strategies(market_intelligence)
        
        # Step 4: Execute trading decisions
        await bot_manager.execute_trading_cycle(market_intelligence)
        
        # Step 5: Log performance and learn
        await self._log_performance_and_learn(cycle_count)
    
    async def _gather_market_intelligence(self) -> Dict[str, Any]:
        """Gather comprehensive market intelligence"""
        intelligence = {
            "timestamp": datetime.utcnow(),
            "market_data": {},
            "sentiment": {},
            "economic_indicators": {},
            "technical_indicators": {},
            "arbitrage_opportunities": [],
            "predictions": {}
        }
        
        try:
            # Get latest market data for all trading pairs
            for symbol in self.config.get_trading_pairs():
                market_data = await market_data_crawler.get_latest_market_data(symbol)
                if market_data:
                    intelligence["market_data"][symbol] = market_data
                
                # Get technical indicators
                indicators = await market_data_crawler.get_market_indicators(symbol, limit=1)
                if indicators:
                    intelligence["technical_indicators"][symbol] = indicators[0]
                
                # Get ML predictions
                predictions = await market_predictor.get_predictions(symbol)
                if predictions:
                    intelligence["predictions"][symbol] = predictions
            
            # Get sentiment analysis
            intelligence["sentiment"] = await news_sentiment_crawler.get_sentiment_summary(6)
            
            # Get arbitrage opportunities
            intelligence["arbitrage_opportunities"] = await market_data_crawler.get_arbitrage_opportunities(0.3)
            
            # Get economic indicators
            intelligence["economic_indicators"] = await economic_data_crawler.get_latest_indicators()
            
        except Exception as e:
            self.logger.error(f"Error gathering market intelligence: {e}")
        
        return intelligence
    
    async def _log_performance_and_learn(self, cycle_count: int):
        """Log performance metrics and trigger learning"""
        try:
            # Get current performance
            performance = await bot_manager.get_performance_metrics()
            
            # Log to database
            await database_manager.execute(
                """
                INSERT INTO performance_log (timestamp, cycle_count, total_trades, profit_loss,
                                           win_rate, avg_trade_duration, current_positions)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                datetime.utcnow(), cycle_count, performance.get("total_trades", 0),
                performance.get("total_pnl", 0), performance.get("win_rate", 0),
                performance.get("avg_trade_duration", 0), performance.get("open_positions", 0)
            )
            
            # Trigger strategy adaptation if needed
            if cycle_count % 100 == 0:  # Every 100 cycles
                await adaptive_strategy_engine.evaluate_and_adapt_strategies()
            
        except Exception as e:
            self.logger.error(f"Error logging performance: {e}")


# Global trading service
trading_service = TradingBotService()


@app.on_event("startup")
async def startup_event():
    """Initialize the advanced trading bot on startup"""
    try:
        await trading_service.initialize()
        # Start trading automatically
        await trading_service.start_trading()
    except Exception as e:
        logging.error(f"Failed to start advanced trading bot: {e}")
        sys.exit(1)


@app.on_event("shutdown")
async def shutdown_event():
    """Gracefully shutdown the advanced trading bot"""
    await trading_service.stop_trading()


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logging.info(f"Received signal {signum}, shutting down advanced trading bot...")
    global is_shutting_down
    is_shutting_down = True


def main():
    """Main entry point"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Run the FastAPI app
    config = BotConfig()
    
    print("🚀 Starting Autonomous Bybit Trading Bot with Advanced Data Crawler")
    print(f"📊 Trading pairs: {', '.join(config.get_trading_pairs())}")
    print(f"🔧 API server: http://{config.api_host}:{config.api_port}")
    print(f"📈 Paper trading: {'Enabled' if config.paper_trading else 'Disabled'}")
    print("=" * 80)
    
    uvicorn.run(
        "main:app",
        host=config.api_host,
        port=config.api_port,
        reload=config.debug_mode,
        log_level="info"
    )


if __name__ == "__main__":
    main()

