#!/usr/bin/env python3
"""
Autonomous Bybit Trading Bot with Advanced Data Crawler
Main Entry Point

Continuously trades on Bybit for profit, adapts strategies using persistent learning,
and monitors system health for safe operation.
Features advanced data crawler for market intelligence.
"""
import asyncio
import time
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, List
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import our trading bot components
from bybit_bot.core.bot_manager import BotManager
from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import setup_logging, TradingBotLogger
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.exchange.bybit_client import BybitClient
from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
from bybit_bot.ml.market_predictor import MLMarketPredictor
from bybit_bot.strategies.strategy_manager import StrategyManager
from bybit_bot.risk.advanced_risk_manager import AdvancedRiskManager
from bybit_bot.analytics.performance_analyzer import PerformanceAnalyzer

# Global variables
bot_manager: Optional[BotManager] = None
hardware_monitor: Optional[HardwareMonitor] = None
database_manager: Optional[DatabaseManager] = None
bybit_client: Optional[BybitClient] = None
market_data_crawler: Optional[MarketDataCrawler] = None
news_sentiment_crawler: Optional[NewsSentimentCrawler] = None
social_sentiment_crawler: Optional[SocialSentimentCrawler] = None
economic_data_crawler: Optional[EconomicDataCrawler] = None
market_predictor: Optional[MLMarketPredictor] = None
strategy_manager: Optional[StrategyManager] = None
risk_manager: Optional[AdvancedRiskManager] = None
performance_analyzer: Optional[PerformanceAnalyzer] = None
trading_bot_service: Optional['TradingBotService'] = None
is_shutting_down = False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global trading_bot_service
    
    # Startup
    try:
        trading_bot_service = TradingBotService()
        await trading_bot_service.initialize()
        await trading_bot_service.start()
        yield
    finally:
        # Shutdown
        if trading_bot_service:
            await trading_bot_service.shutdown()


# FastAPI app with lifespan
app = FastAPI(
    title="Autonomous Bybit Trading Bot with Advanced Data Crawler",
    description="Continuously trades on Bybit for profit with adaptive learning and advanced market intelligence",
    version="2.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# === API ROUTES ===

@app.get("/")
async def root():
    """Root endpoint with bot status"""
    return {
        "message": "Autonomous Bybit Trading Bot with Advanced Data Crawler",
        "status": "running" if bot_manager and bot_manager.running else "stopped",
        "timestamp": datetime.utcnow().isoformat(),
        "features": [
            "Real-time market data collection",
            "News sentiment analysis", 
            "Social media sentiment tracking",
            "Economic indicators monitoring",
            "Machine learning predictions",
            "Adaptive strategy optimization",
            "Advanced risk management",
            "Hardware health monitoring",
            "Cross-exchange arbitrage detection"
        ]
    }


@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    health_status = {
        "overall": "healthy",
        "components": {},
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Check hardware monitor
    if hardware_monitor:
        hw_status = await hardware_monitor.get_system_status()
        health_status["components"]["hardware"] = hw_status
        if hw_status.get("overall_status") != "healthy":
            health_status["overall"] = "degraded"
    
    # Check database
    if database_manager:
        try:
            await database_manager.execute("SELECT 1")
            health_status["components"]["database"] = "healthy"
        except Exception:
            health_status["components"]["database"] = "unhealthy"
            health_status["overall"] = "unhealthy"
    
    # Check trading bot
    if bot_manager:
        health_status["components"]["trading_bot"] = {
            "status": "running" if bot_manager.running else "stopped",
            "uptime": time.time() - bot_manager.start_time if hasattr(bot_manager, 'start_time') else 0
        }
    
    # Check data crawlers
    crawlers = {
        "market_data": market_data_crawler,
        "news_sentiment": news_sentiment_crawler,
        "social_sentiment": social_sentiment_crawler,
        "economic_data": economic_data_crawler
    }
    
    for name, crawler in crawlers.items():
        health_status["components"][f"{name}_crawler"] = "running" if crawler and crawler.running else "stopped"
    
    # Check ML predictor
    if market_predictor:
        health_status["components"]["ml_predictor"] = "active" if market_predictor.running else "inactive"
    
    # Check risk manager
    if risk_manager:
        health_status["components"]["risk_manager"] = "active" if risk_manager.running else "inactive"
    
    return health_status


@app.get("/status")
async def get_comprehensive_status():
    """Get comprehensive system status"""
    status = {
        "timestamp": datetime.utcnow().isoformat(),
        "system": {},
        "trading": {},
        "data_crawlers": {},
        "ml_models": {},
        "risk_management": {},
        "market_data": {}
    }
    
    # Trading status
    if bot_manager:
        status["trading"] = await bot_manager.get_status()
    
    # Hardware status
    if hardware_monitor:
        status["system"] = await hardware_monitor.get_system_status()
    
    # Risk management status
    if risk_manager:
        status["risk_management"] = await risk_manager.get_risk_summary()
    
    # Data crawler status
    if market_data_crawler:
        status["data_crawlers"]["market_data"] = {
            "running": market_data_crawler.running,
            "last_update": "real-time"
        }
    
    if news_sentiment_crawler:
        sentiment_summary = await news_sentiment_crawler.get_sentiment_summary(24)
        status["data_crawlers"]["news_sentiment"] = {
            "running": news_sentiment_crawler.running,
            "sentiment_summary": sentiment_summary
        }
    
    if social_sentiment_crawler:
        social_summary = await social_sentiment_crawler.get_social_sentiment_summary(hours=24)
        status["data_crawlers"]["social_sentiment"] = {
            "running": social_sentiment_crawler.running,
            "social_summary": social_summary
        }
    
    if economic_data_crawler:
        economic_summary = await economic_data_crawler.get_economic_summary(days=7)
        status["data_crawlers"]["economic_data"] = {
            "running": economic_data_crawler.running,
            "economic_summary": economic_summary
        }
    
    # ML model status
    if market_predictor:
        status["ml_models"] = {
            "running": market_predictor.running,
            "models_loaded": len(market_predictor.models),
            "last_prediction": "real-time"
        }
    
    # Recent market data
    if market_data_crawler:
        for symbol in ["BTCUSDT", "ETHUSDT"]:
            latest_data = await market_data_crawler.get_latest_market_data(symbol)
            if latest_data:
                status["market_data"][symbol] = {
                    "price": latest_data.get("close_price"),
                    "change_24h": latest_data.get("change_percent_24h"),
                    "volume": latest_data.get("volume"),
                    "last_update": latest_data.get("timestamp")
                }
    
    return status


@app.get("/market-data/{symbol}")
async def get_market_data(symbol: str):
    """Get latest market data for a symbol"""
    if not market_data_crawler:
        raise HTTPException(status_code=503, detail="Market data crawler not available")
    
    latest_data = await market_data_crawler.get_latest_market_data(symbol.upper())
    if not latest_data:
        raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
    
    # Get additional indicators
    indicators = await market_data_crawler.get_market_indicators(symbol.upper(), limit=1)
    
    return {
        "symbol": symbol.upper(),
        "market_data": latest_data,
        "indicators": indicators[0] if indicators else None,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/sentiment")
async def get_sentiment_analysis():
    """Get current sentiment analysis"""
    result = {
        "news_sentiment": {},
        "social_sentiment": {},
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # News sentiment
    if news_sentiment_crawler:
        sentiment_summary = await news_sentiment_crawler.get_sentiment_summary(24)
        trending_topics = await news_sentiment_crawler.get_trending_topics(10)
        significant_news = await news_sentiment_crawler.get_news_by_sentiment(0.5, 10)
        
        result["news_sentiment"] = {
            "sentiment_summary": sentiment_summary,
            "trending_topics": trending_topics,
            "significant_news": significant_news
        }
    
    # Social sentiment
    if social_sentiment_crawler:
        social_summary = await social_sentiment_crawler.get_social_sentiment_summary(hours=24)
        trending_social = await social_sentiment_crawler.get_trending_social_topics(10)
        influencer_sentiment = await social_sentiment_crawler.get_influencer_sentiment(20)
        
        result["social_sentiment"] = {
            "social_summary": social_summary,
            "trending_topics": trending_social,
            "influencer_sentiment": influencer_sentiment
        }
    
    return result


@app.get("/economic-data")
async def get_economic_data():
    """Get current economic data and analysis"""
    if not economic_data_crawler:
        raise HTTPException(status_code=503, detail="Economic data crawler not available")
    
    economic_summary = await economic_data_crawler.get_economic_summary(days=7)
    
    return {
        "economic_data": economic_summary,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/arbitrage")
async def get_arbitrage_opportunities():
    """Get current arbitrage opportunities"""
    if not market_data_crawler:
        raise HTTPException(status_code=503, detail="Market data crawler not available")
    
    opportunities = await market_data_crawler.get_arbitrage_opportunities(min_profit=0.5)
    
    return {
        "opportunities": opportunities,
        "count": len(opportunities),
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/predictions/{symbol}")
async def get_price_predictions(symbol: str):
    """Get ML price predictions for a symbol"""
    if not market_predictor:
        raise HTTPException(status_code=503, detail="ML predictor not available")
    
    predictions = await market_predictor.get_prediction_summary(symbol.upper())
    
    return {
        "symbol": symbol.upper(),
        "predictions": predictions,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/risk-analysis")
async def get_risk_analysis():
    """Get comprehensive risk analysis"""
    if not risk_manager:
        raise HTTPException(status_code=503, detail="Risk manager not available")
    
    risk_summary = await risk_manager.get_risk_summary()
    
    return {
        "risk_analysis": risk_summary,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/performance")
async def get_performance_metrics():
    """Get trading performance metrics"""
    if not performance_analyzer:
        raise HTTPException(status_code=503, detail="Performance analyzer not available")
    
    performance = await performance_analyzer.get_performance_summary()
    
    return {
        "performance": performance,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/control/start")
async def start_trading():
    """Start trading endpoint"""
    try:
        if bot_manager:
            await bot_manager.start_trading()
            return {"message": "Trading started successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="Bot manager not available")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start trading: {e}")


@app.post("/control/stop")
async def stop_trading():
    """Stop trading endpoint"""
    try:
        if bot_manager:
            await bot_manager.stop_trading()
            return {"message": "Trading stopped successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="Bot manager not available")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop trading: {e}")


@app.post("/control/retrain-models")
async def retrain_ml_models():
    """Retrain ML models endpoint"""
    try:
        if market_predictor:
            # Trigger model retraining
            await market_predictor._initialize_models()
            return {"message": "ML models retrained successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="ML predictor not available")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrain models: {e}")


@app.post("/control/reset-emergency-stop")
async def reset_emergency_stop():
    """Reset emergency stop"""
    try:
        if risk_manager:
            risk_manager.reset_emergency_stop()
            return {"message": "Emergency stop reset successfully", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="Risk manager not available")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset emergency stop: {e}")


# === TRADING BOT SERVICE ===

class TradingBotService:
    """Advanced trading bot service orchestrating all components"""
    
    def __init__(self):
        self.config = None
        self.logger = None
        self.initialized = False
        
    async def initialize(self):
        """Initialize all bot components"""
        global bot_manager, hardware_monitor, database_manager, bybit_client, \
            market_data_crawler, news_sentiment_crawler, social_sentiment_crawler, \
            economic_data_crawler, market_predictor, strategy_manager, \
            risk_manager, performance_analyzer
        
        try:
            print("🚀 Initializing Autonomous Bybit Trading Bot...")
            
            # Load configuration
            self.config = BotConfig()
            
            # Setup logging
            setup_logging(
                log_level=self.config.log_level,
                log_file=self.config.log_file,
                console_output=True
            )
            
            self.logger = TradingBotLogger(self.config)
            self.logger.info("🔧 Starting system initialization...")
            
            # Initialize database
            database_manager = DatabaseManager(self.config)
            await database_manager.initialize()
            self.logger.info("✅ Database initialized")
            
            # Initialize hardware monitor
            hardware_monitor = HardwareMonitor(self.config)
            await hardware_monitor.start()
            self.logger.info("✅ Hardware monitor started")
            
            # Initialize Bybit client
            bybit_client = BybitClient(self.config)
            await bybit_client.initialize()
            self.logger.info("✅ Bybit client initialized")
            
            # Initialize data crawlers
            market_data_crawler = MarketDataCrawler(self.config, database_manager)
            news_sentiment_crawler = NewsSentimentCrawler(self.config, database_manager)
            social_sentiment_crawler = SocialSentimentCrawler(self.config, database_manager)
            economic_data_crawler = EconomicDataCrawler(self.config, database_manager)
            self.logger.info("✅ Data crawlers initialized")
            
            # Initialize ML predictor
            market_predictor = MLMarketPredictor(self.config, database_manager)
            await market_predictor._initialize_models()
            self.logger.info("✅ ML predictor initialized")
            
            # Initialize risk manager
            risk_manager = AdvancedRiskManager(self.config, database_manager, bybit_client)
            await risk_manager.initialize()
            self.logger.info("✅ Risk manager initialized")
            
            # Initialize strategy manager
            strategy_manager = StrategyManager(self.config, database_manager, market_predictor, risk_manager)
            await strategy_manager.initialize()
            self.logger.info("✅ Strategy manager initialized")
            
            # Initialize performance analyzer
            performance_analyzer = PerformanceAnalyzer(self.config, database_manager)
            await performance_analyzer.initialize()
            self.logger.info("✅ Performance analyzer initialized")
            
            # Initialize bot manager (main orchestrator)
            bot_manager = BotManager(
                config=self.config,
                database_manager=database_manager,
                bybit_client=bybit_client,
                strategy_manager=strategy_manager,
                risk_manager=risk_manager,
                performance_analyzer=performance_analyzer
            )
            await bot_manager.initialize()
            self.logger.info("✅ Bot manager initialized")
            
            self.initialized = True
            self.logger.info("🎉 System initialization complete!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize system: {e}")
            raise
    
    async def start(self):
        """Start the trading bot and all components"""
        if not self.initialized:
            raise RuntimeError("System not initialized")
        
        try:
            self.logger.info("🚀 Starting all trading bot components...")
            
            # Start data crawlers
            await market_data_crawler.start()
            await news_sentiment_crawler.start()
            await social_sentiment_crawler.start()
            await economic_data_crawler.start()
            self.logger.info("✅ Data crawlers started")
            
            # Start ML predictor
            await market_predictor.start()
            self.logger.info("✅ ML predictor started")
            
            # Start bot manager (this starts the main trading loop)
            await bot_manager.start()
            self.logger.info("✅ Bot manager started")
            
            self.logger.info("🎯 Autonomous trading bot is now running!")
            self.logger.info("📊 Monitor progress at: http://localhost:8000")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start trading bot: {e}")
            raise
    
    async def shutdown(self):
        """Graceful shutdown of all components"""
        global is_shutting_down
        
        is_shutting_down = True
        self.logger.info("🛑 Initiating graceful shutdown...")
        
        try:
            # Stop bot manager first
            if bot_manager:
                await bot_manager.shutdown()
            
            # Stop data crawlers
            if market_data_crawler:
                await market_data_crawler.stop()
            if news_sentiment_crawler:
                await news_sentiment_crawler.stop()
            if social_sentiment_crawler:
                await social_sentiment_crawler.stop()
            if economic_data_crawler:
                await economic_data_crawler.stop()
            
            # Stop ML predictor
            if market_predictor:
                await market_predictor.stop()
            
            # Stop risk manager
            if risk_manager:
                await risk_manager.stop_monitoring()
            
            # Stop hardware monitor
            if hardware_monitor:
                await hardware_monitor.stop()
            
            # Close database connections
            if database_manager:
                await database_manager.close()
            
            # Close Bybit client
            if bybit_client:
                await bybit_client.close()
            
            self.logger.info("✅ Graceful shutdown complete")
            
        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")


# === MAIN EXECUTION ===

async def main():
    """Main execution function"""
    try:
        # Create and initialize the trading bot service
        service = TradingBotService()
        await service.initialize()
        await service.start()
        
        # Start the FastAPI server
        config = uvicorn.Config(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        server = uvicorn.Server(config)
        
        print("🌐 Starting FastAPI server on http://localhost:8000")
        await server.serve()
        
    except KeyboardInterrupt:
        print("\n🛑 Received shutdown signal...")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        logging.exception("Fatal error in main")
    finally:
        print("👋 Goodbye!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Shutdown complete")
