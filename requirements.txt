# Autonomous Bybit Trading Bot Dependencies with Advanced Data Crawler

# Core Trading & API
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0
websockets==12.0
pybit==5.7.0
ccxt==4.1.77

# Database & Storage
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis[hiredis]==5.0.1

# Data Processing & Analysis
numpy==1.25.2
pandas==2.1.4
ta-lib==0.4.28
ta==0.10.2
scipy==1.11.4
yfinance==0.2.28

# Machine Learning & AI
scikit-learn==1.3.2
tensorflow==2.15.0
torch==2.1.2
transformers==4.36.2
xgboost==2.0.2
lightgbm==4.1.0
joblib==1.3.2

# Async & Background Tasks
celery==5.3.4
APScheduler==3.10.4

# Monitoring & Hardware
psutil==5.9.6
GPUtil==1.4.0
py-cpuinfo==9.0.0
prometheus-client==0.19.0

# News & Sentiment Analysis
newspaper3k==0.2.8
textblob==0.17.1
vaderSentiment==3.3.2
beautifulsoup4==4.12.2
feedparser==6.0.10
lxml==4.9.3

# Social Media APIs
tweepy==4.14.0
praw==7.7.1

# Economic Data APIs
alpha-vantage==2.3.1
fredapi==0.5.1
quandl==3.7.0
investpy==1.0.8

# Configuration & Utilities
python-dotenv==1.0.0
PyYAML==6.0.1
click==8.1.7
rich==13.7.0
colorama==0.4.6
pytz==2023.3

# Logging & Monitoring
structlog==23.2.0

# Security
cryptography==41.0.8
bcrypt==4.1.2

# Data Visualization
plotly==5.17.0
matplotlib==3.8.2

# File Processing
openpyxl==3.1.2
xlsxwriter==3.1.9

# API Rate Limiting
ratelimit==2.2.1
backoff==2.2.1

# Testing & Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0