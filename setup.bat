@echo off
REM Autonomous Bybit Trading Bot Setup Script
REM This script sets up the conda environment and installs dependencies on the E drive

echo ================================
echo Autonomous Bybit Trading Bot Setup
echo ================================

REM Check if we're on E drive
if not "%~d0"=="E:" (
    echo Switching to E drive...
    E:
    cd \The_real_deal_copy\Bybit_Bot\BOT
)

echo Current directory: %cd%

REM Check if conda is installed
where conda >nul 2>nul
if errorlevel 1 (
    echo ERROR: Conda not found in PATH
    echo Please install Anaconda or Miniconda first
    pause
    exit /b 1
)

echo ✅ Conda found

REM Create conda environment on E drive
echo 📦 Creating conda environment 'bybit-trader'...
conda create -n bybit-trader python=3.10 -y

if errorlevel 1 (
    echo ❌ Failed to create conda environment
    pause
    exit /b 1
)

echo ✅ Conda environment created

REM Activate environment and install dependencies
echo 📥 Installing dependencies...
call conda activate bybit-trader

if errorlevel 1 (
    echo ❌ Failed to activate conda environment
    pause
    exit /b 1
)

REM Upgrade pip first
python -m pip install --upgrade pip

REM Install requirements
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo ✅ Dependencies installed

REM Create necessary directories
echo 📁 Creating directories...
if not exist "E:\bybit_bot_workspace" mkdir "E:\bybit_bot_workspace"
if not exist "E:\bybit_bot_data" mkdir "E:\bybit_bot_data"
if not exist "E:\bybit_bot_logs" mkdir "E:\bybit_bot_logs"
if not exist "E:\bybit_bot_models" mkdir "E:\bybit_bot_models"

echo ✅ Directories created

REM Set up PostgreSQL database (optional)
echo 🗄️ Setting up database...
echo NOTE: Make sure PostgreSQL is installed and running
echo Default database will be created as 'bybit_trading_bot'

REM Create database initialization script
echo -- Database initialization for Bybit Trading Bot > init_db.sql
echo CREATE DATABASE bybit_trading_bot; >> init_db.sql
echo \c bybit_trading_bot; >> init_db.sql
echo CREATE EXTENSION IF NOT EXISTS "uuid-ossp"; >> init_db.sql

echo ✅ Database initialization script created (init_db.sql)

echo.
echo ================================
echo 🎯 Setup Complete!
echo ================================
echo.
echo Next steps:
echo 1. Edit .env file with your Bybit API keys
echo 2. Set up PostgreSQL database (run init_db.sql)
echo 3. Run: conda activate bybit-trader
echo 4. Run: python main.py
echo.
echo Configuration file: .env
echo Logs directory: E:\bybit_bot_logs
echo Data directory: E:\bybit_bot_data
echo.

pause
